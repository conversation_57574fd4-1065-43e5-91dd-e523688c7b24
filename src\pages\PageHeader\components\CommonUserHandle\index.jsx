import PropTypes from 'prop-types';
import React, { Component, Fragment } from 'react';
import { string } from 'prop-types';
import { Icon, Tooltip, MdLink } from 'ming-ui';
import styled from 'styled-components';
import Avatar from '../Avatar';
import UserMenu from '../UserMenu';
import AddMenu from '../AddMenu';
import MyProcessEntry from '../MyProcessEntry';
import CreateAppItem from './CreateAppItem';
import HelpCollection from './HelpCollection';
import { canEditApp, canEditData } from 'src/pages/worksheet/redux/actions/util.js';
import './index.less';
import { getAppFeaturesVisible } from 'src/util';
import _ from 'lodash';
import GlobalSearch from '../GlobalSearch/index';
import { withRouter } from 'react-router-dom';
import appManagementApi from 'src/api/appManagement';
import privateSysSettingApi from 'src/api/privateSysSetting';
import { VerticalMiddle } from 'worksheet/components/Basics';
import cx from 'classnames';
import { hasBackStageAdminAuth } from 'src/components/checkPermission';
import Trigger from 'rc-trigger';
import HapAiDialog from './HapAiDialog';
import hapAI from './images/hapAI.png';
import AI_logo from 'staticfiles/images/AIGIF.gif';
import { useAccountInfoStore } from '../../store/accountInfoStore';
import default_avatar from 'staticfiles/images/default_avatar.png';
import { menuList } from 'src/pages/Admin/router.config.js';
import { ROUTE_CONFIG } from 'src/pages/Admin/enum';
import { getMyPermissions } from 'src/components/checkPermission';
import { navigateTo } from 'src/router/navigateTo';
import { emitter } from 'src/util';

const AdminEntry = styled(VerticalMiddle)`
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 28px;
  height: 28px;
  border-radius: 28px;
  margin: 0 5px;
  .icon {
    font-size: 20px;
    color: rgb(0, 0, 0, 0.6);
  }
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
`;

const EntryWrap = styled.div`
  height: 32px;
  line-height: 32px;
  border-radius: 20px 20px 20px 20px;
  padding: 0 10px;
  cursor: pointer;
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
  display: flex;
  align-items: center;
`;

const HapAi = styled.div`
  height: 32px;
  border-radius: 16px;
  padding: 6px 15px 0;
  cursor: pointer;
  .hapAI {
    width: 20px;
    height: 20px;
  }
  &:hover {
    background: rgba(0, 0, 0, 0.05);
  }
`;

@withRouter
export default class CommonUserHandle extends Component {
  unsubscribe;

  static propTypes = {
    type: string,
    currentProject: PropTypes.shape({}),
  };
  state = {
    globalSearchVisible: false,
    userVisible: false,
    newVersion: null,
    storeAvatar: useAccountInfoStore.getState().avatar,
    path: '',
    drawerState: false,
  };
  constructor(props) {
    super(props);
    this.handleDrawerStateChange = this.handleDrawerStateChange.bind(this);
  }
  async componentDidMount() {
    this.unsubscribe = useAccountInfoStore.subscribe(() => {
      this.forceUpdate();
    });

    if (md.global.SysSettings.enablePromptNewVersion && md.global.Account.superAdmin) {
      privateSysSettingApi.getNewVersionInfo().then(data => {
        this.setState({ newVersion: data });
      });
    }

    // 组织管理跳转路径预加载
    const { currentProject = {} } = this.props;
    if (currentProject.projectId) {
      const path = await this.getFirstAdminPage(currentProject.projectId);
      this.setState({ path });
    }
    emitter.addListener('CHANGE_DRAWER_STATE_CB', this.handleDrawerStateChange);
  }

  componentWillUnmount() {
    this.unsubscribe();
    emitter.removeListener('CHANGE_DRAWER_STATE_CB', this.handleDrawerStateChange);
  }

  handleDrawerStateChange(state) {
    this.setState({ drawerState: state });
  }

  handleUserVisibleChange(visible) {
    this.setState({
      userVisible: visible,
    });
  }

  openGlobalSearch() {
    this.setState({ globalSearchVisible: true });
    GlobalSearch({
      match: this.props.match,
      onClose: () => this.setState({ globalSearchVisible: false }),
    });
  }

  handleAddMenuVisible(visible) {
    this.setState({
      addMenuVisible: visible,
    });
  }

  // 获取有权限的第一个组织管理页面
  getFirstAdminPage = async (projectId) => {
    // 获取权限
    let myPermissions = await getMyPermissions(projectId, false); // 异步获取
    let keys = [];
    myPermissions.forEach(item => {
      keys = keys.concat(ROUTE_CONFIG[item] || []);
    });
    keys = _.uniq(keys);
    for (let menu of menuList) {
      for (let sub of menu.subMenuList) {
        if (keys.includes(sub.key)) {
          // 找到第一个有权限的子模块
          const firstRoute = sub.routes[0];
          if (!firstRoute) continue;
          // 替换 path 里的 :projectId 或 (.*)
          let path = firstRoute.path;
          if (path.includes(':projectId')) {
            path = path.replace(':projectId', projectId);
          } else if (path.includes('(.*)')) {
            path = path.replace('(.*)', projectId);
          }
          // 去掉多余的参数
          path = path.replace(/\(.*\)/, '');
          return path;
        }
      }
    }
    // 没有权限则返回 null
    return null;
  };

  changeState(){
    const { drawerState } = this.state;
    emitter.emit('CHANGE_DRAWER_STATE', !drawerState)
    this.setState({ drawerState: !drawerState });
  }

  render() {
    const { globalSearchVisible, userVisible, popupVisible, showHapAi, newVersion, path, changeState } = this.state;
    const { type, currentProject = {} } = this.props;
    const hasProjectAdminAuth =
      currentProject.projectId &&
      currentProject.projectId !== 'external' &&
      hasBackStageAdminAuth({ projectId: currentProject.projectId });
    const storeAvatarValue = useAccountInfoStore.getState().avatar;

    // 获取url参数
    const { tr, ss, ac } = getAppFeaturesVisible();
    if (window.isPublicApp || !tr) {
      return null;
    }

    return (
      <div className={cx('commonUserHandleWrap', { dashboardCommonUserHandleWrap: type === 'dashboard' })}>
        {type === 'native' && (
          <React.Fragment>
            <Tooltip
              popupVisible={this.state.addMenuVisible}
              text={
                <AddMenu
                  onClose={() => {
                    this.setState({ addMenuVisible: false });
                  }}
                />
              }
              action={['click']}
              mouseEnterDelay={0.2}
              themeColor="white"
              tooltipClass="pAll0"
              onPopupVisibleChange={this.handleAddMenuVisible.bind(this)}
            >
              <div className="addOperationIconWrap mLeft20 mRight15 pointer">
                <Icon icon="addapplication Font30" />
              </div>
            </Tooltip>
            <MyProcessEntry type={type} />
          </React.Fragment>
        )}

        {ss && type === 'appPkg' && (
          <React.Fragment>
            {md.global.Config.IsLocal ? (
              !md.global.SysSettings.hideHelpTip ? (
                <div
                  className="workflowHelpIconWrap pointer"
                  data-tip={_l('帮助')}
                  onClick={() => window.open('https://help.mingdao.com')}
                >
                  <Icon icon="workflow_help" className="helpIcon Font20" />
                </div>
              ) : (
                ''
              )
            ) : (
              <Trigger
                action={['click']}
                popupVisible={popupVisible}
                onPopupVisibleChange={popupVisible => this.setState({ popupVisible })}
                popup={
                  <HelpCollection
                    hapAIPosition="top"
                    updatePopupVisible={popupVisible => this.setState({ popupVisible })}
                  />
                }
                popupAlign={{
                  points: ['tr', 'br'],
                  offset: [20, 11],
                  overflow: { adjustX: true, adjustY: true },
                }}
              >
                <div
                  className="workflowHelpIconWrap pointer"
                  data-tip={_l('帮助')}
                  onClick={() => this.setState({ popupVisible: true })}
                >
                  <Icon icon="workflow_help" className="helpIcon Font20" />
                </div>
              </Trigger>
            )}
          </React.Fragment>
        )}

        {type !== 'appPkg' && (
          <React.Fragment>

            {!md.global.Config.IsLocal && type === 'dashboard' && (
              <HapAi onClick={() => this.setState({ showHapAi: true })}>
                <img className="hapAI" src={hapAI} />
                <span className="Gray_75 mLeft6">{_l('HAP助手')}</span>
              </HapAi>
            )}

            {type === 'dashboard' && (<EntryWrap onClick={() => this.changeState()}>
              <img className="TxtMiddles" src={AI_logo} height={20} width={20}/>
              <span className="Gray_75 mLeft5 TxtMiddle">{_l('AI速搭')}</span>
            </EntryWrap>)}

            {!!window.config.helpFilePath && (
              <EntryWrap onClick={() => window.open(window.config.helpFilePath)}>
                <Icon icon="workflow_help" className="helpIcon Font20 Gray_75 TxtMiddle" />
                <span className="Gray_75 mLeft5 TxtMiddle">{_l('帮助')}</span>
              </EntryWrap>
            )}
            {!md.global.Config.IsLocal && (
              <Trigger
                action={['click']}
                popupVisible={popupVisible}
                onPopupVisibleChange={popupVisible => this.setState({ popupVisible })}
                popup={
                  <HelpCollection
                    hapAIPosition="top"
                    updatePopupVisible={popupVisible => this.setState({ popupVisible })}
                  />
                }
                popupAlign={{
                  points: ['tr', 'br'],
                  offset: [40, 9],
                  overflow: { adjustX: true, adjustY: true },
                }}
              >
                <EntryWrap onClick={() => this.setState({ popupVisible: true })}>
                  <Icon icon="workflow_help" className="helpIcon Font20 Gray_75 TxtMiddle" />
                  <span className="Gray_75 mLeft5 TxtMiddle">{_l('帮助')}</span>
                </EntryWrap>
              </Trigger>
            )}

            {type === 'dashboard' && (
              <MdLink to={`/datasource`}>
                <EntryWrap>
                  <i className="icon icon-widgets Font20 Gray_75 TxtMiddle"></i>
                  <span className="Gray_75 mLeft5 TxtMiddle">{_l('对象管理')}</span>
                </EntryWrap>
              </MdLink>
            )}

            {type === 'dashboard' && hasProjectAdminAuth && path &&
              <EntryWrap
                onClick={async () => {
                  navigateTo(path);
                }}
              >
                <i className="icon icon-business Font20 Gray_75 TxtMiddle"></i>
                <span className="Gray_75 mLeft5 TxtMiddle">{_l('组织管理')}</span>
              </EntryWrap>
            }

            {type === 'dashboard' && newVersion && (
              <AdminEntry
                data-tip={_l('发现新版本：%0，点击查看', newVersion)}
                className="tip-bottom-left"
                onClick={() => window.open('https://docs-pd.mingdao.com/version')}
              >
                <Icon icon="score-up" className="Font20" style={{ color: '#20CA86' }} />
              </AdminEntry>
            )}
          </React.Fragment>
        )}

        {ac && (
          <Tooltip
            text={<UserMenu handleUserVisibleChange={this.handleUserVisibleChange.bind(this)} />}
            action={['click']}
            themeColor="white"
            tooltipClass="pageHeadUser commonHeaderUser Normal"
            getPopupContainer={() => this.avatar}
            popupPlacement="bottom"
            offset={[-110, 0]}
            popupVisible={userVisible}
            onPopupVisibleChange={this.handleUserVisibleChange.bind(this)}
          >
            <div
              ref={avatar => {
                this.avatar = avatar;
              }}
            >
              <span className="tip-bottom-left mLeft16" data-tip={md.global.Account.fullname}>
                <Avatar src={storeAvatarValue} size={30} />
              </span>
            </div>
          </Tooltip>
        )}

        {showHapAi && <HapAiDialog visible={showHapAi} onCancel={() => this.setState({ showHapAi: false })} />}
      </div>
    );
  }
}

@withRouter
export class LeftCommonUserHandle extends Component {
  static propTypes = {
    type: string,
  };
  state = {
    globalSearchVisible: false,
    userVisible: false,
    roleEntryVisible: true,
  };

  componentDidMount() {
    const { id, permissionType, isLock } = this.props.data;
    if (!canEditData(permissionType) && !canEditApp(permissionType, isLock)) {
      appManagementApi
        .getAppRoleSetting({
          appId: id,
        })
        .then(data => {
          const { appSettingsEnum } = data;
          this.setState({ roleEntryVisible: appSettingsEnum === 1 });
        });
    }
  }

  handleUserVisibleChange(visible) {
    this.setState({
      userVisible: visible,
    });
  }
  openGlobalSearch() {
    this.setState({ globalSearchVisible: true });
    GlobalSearch({
      match: this.props.match,
      onClose: () => this.setState({ globalSearchVisible: false }),
    });
  }

  render() {
    const { globalSearchVisible, userVisible, roleEntryVisible, popupVisible } = this.state;
    const { isAuthorityApp, type, data, sheet, match } = this.props;
    const { projectId, id, permissionType, isLock, appStatus, fixed, pcDisplay } = data;
    const isUpgrade = appStatus === 4;
    // 获取url参数
    const { tr, ss, ac } = getAppFeaturesVisible();
    if (window.isPublicApp || !tr) {
      return null;
    }

    return (
      <div className="commonUserHandleWrap leftCommonUserHandleWrap w100">
        {!isUpgrade && (
          <Fragment>
            <CreateAppItem
              isCharge={sheet.isCharge}
              appId={id}
              groupId={match.params.groupId}
              worksheetId={match.params.worksheetId}
              projectId={projectId}
            >
              <div className="headerColorSwitch">
                <Icon icon="add" className="Font20 pointer" />
              </div>
            </CreateAppItem>
            {
              _.includes([1, 5], appStatus) && !md.global.Account.isPortal && (
              <Fragment>
                {!window.isPublicApp && canEditApp(permissionType, isLock) && (
                  <MdLink data-tip={_l('工作流')} className="tip-top" to={`/app/${id}/workflow`}>
                    <Icon icon="workflow" className="Font20 headerColorSwitch" />
                  </MdLink>
                )}
                {roleEntryVisible && (
                  <MdLink data-tip={_l('用户')} className="tip-top" to={`/app/${id}/role`}>
                    <Icon icon="group" className="Font20 headerColorSwitch" />
                  </MdLink>
                )}

                {!!window.config.helpFilePath && (
                  <div
                    className="tip-top pointer"
                    data-tip={_l('帮助')}
                    onClick={() => window.open(window.config.helpFilePath)}
                  >
                    <Icon icon="workflow_help" className="Font20 headerColorSwitch" />
                  </div>
                )}
              </Fragment>
            )}
          </Fragment>
        )}
        {ac && (
          <Tooltip
            text={
              <UserMenu
                handleUserVisibleChange={this.handleUserVisibleChange.bind(this)}
                leftCommonUserHandleWrap={true}
              />
            }
            action={['click']}
            themeColor="white"
            tooltipClass="pageHeadUser"
            getPopupContainer={() => this.avatar}
            offset={[160, 50]}
            popupVisible={userVisible}
            onPopupVisibleChange={this.handleUserVisibleChange.bind(this)}
          >
            <div
              ref={avatar => {
                this.avatar = avatar;
              }}
            >
              <span className="tip-top" data-tip={md.global.Account.fullname}>
                {/* <Avatar src={md.global.Account.avatar.replace(/w\/100\/h\/100/, 'w/90/h/90')} size={30} /> */}
                <Avatar src={md.global.Account.avatar || default_avatar} size={30} />
              </span>
            </div>
          </Tooltip>
        )}
      </div>
    );
  }
}
