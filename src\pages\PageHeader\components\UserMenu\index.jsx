import React, { useState, useEffect } from 'react';
import './index.less';
import login from 'src/api/login';
import { loginOut } from 'src/api-manas/login';
import { navigateTo } from 'src/router/navigateTo';
import { Support, Tooltip, LoadDiv, Dialog } from 'ming-ui';
import cx from 'classnames';
import { removePssId } from 'src/util/pssId';
import { removePssId as manasRemovePssId } from 'src/util/manasPssId';
import _ from 'lodash';
import weixin from 'src/api/weixin';
import langConfig from 'src/common/langConfig';
import accountSetting from 'src/api/accountSetting';
import { navigateToLogin } from 'src/router/navigateTo';
import localForage from 'localforage';
import { useAccountInfoStore } from '../../store/accountInfoStore';
import Avatar from '../Avatar';

export default function UserMenu(props) {
  const [showDialog, setShowDialog] = useState(false);
  const [code, setCode] = useState('');
  const fullname = useAccountInfoStore((state) => state.fullname);
  const logout = () => {
    window.currentLeave = true;

    loginOut().then(data => {
      if (data) {
        localForage.clear();
        // removePssId();
        manasRemovePssId();
        window.localStorage.removeItem('LoginCheckList'); // accountId 和 encryptPassword 清理掉
        window.localStorage.removeItem('AI_TOKEN');
        navigateToLogin({ needReturnUrl: false, redirectUrl: _.isObject(data) ? data.redirectUrl : undefined });
      }
    });
  };


  useEffect(() => {
    if (!showDialog || code) return;

    weixin.getWeiXinServiceNumberQRCode().then(function (data) {
      setCode(data);
    });
  }, [showDialog]);

  return (
    <div id="userSet">
      <div className="flexRow accountInfo">
        <Avatar
          src={md.global.Account.avatar}
          size={40}
          onClick={() => {
            navigateTo('/personal?type=information');
          }}
        />
        <div className="mLeft12">
          <div className="Font14 bold">{fullname}</div>
          <div className="Gray_75 mTop4">{md.global.Account.mobilePhone}</div>
        </div>
      </div>
      <ul className="userSetUL">
        <li data-tag="account">
          <a href="/personal?type=information" className="Relative">
            <span className="icon icon-account_circle" />
            <span className="TxtMiddle">{_l('个人账户')}</span>
          </a>
        </li>
      </ul>

      <ul className="userSetUL">
        <li>
          <a onClick={logout} rel="external">
            <span className="icon icon-logout" />
            <span className="TxtMiddle">{_l('安全退出')}</span>
          </a>
        </li>
      </ul>

      {showDialog && (
        <Dialog
          visible
          title={_l('关注服务号')}
          width={400}
          footer={null}
          handleClose={() => setShowDialog(false)}
        >
          <div className="flexRow alignItemsCenter">
            <div className="flexColumn justifyContentCenter" style={{ width: 100, height: 100 }}>
              {code ? <img src={code} width="100" height="100" /> : <LoadDiv />}
            </div>
            <div className="flex">{_l('用微信【扫一扫】二维码')}</div>
          </div>
        </Dialog>
      )}
    </div>
  );
}
