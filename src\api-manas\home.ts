const portalApi = 'portal'

const instanceApi = 'workflow'; // 流程

// 流程待办
export const getTodoCount = async (): Promise<API.Response<GetTodoCountVO>> => {
  return manasAPI<GetTodoCountVO>(instanceApi, 'instance/getTodoCount', {}, {
    ajaxOptions: { type: 'GET'}
  })
}

// 获取资源列表
export const getPrivateSource = async (
  data: GetPrivateSourceDTO,
  options: API.ManasAPIOptions = {}
): Promise<API.Response<GetPrivateSourceVO[]>> => {
  return manasAPI<GetPrivateSourceVO[]>(portalApi, 'home/getPrivateSource', data, {
    ...options,
    ajaxOptions: {
      type: 'POST',
      ...options.ajaxOptions
    }
  });
}

// 获取平台设置
export const myPlatform  = async (data: MyPlatformDTO): Promise<API.Response<MyPlatformVO>> => {
  return manasAPI<MyPlatformVO>(portalApi, 'home/myPlatform', data, {
    ajaxOptions: { type: 'POST'}
  }) ?? false;
}

// 修改首页自定义显示设置
export const editHomeSetting = async (
  data: EditHomeSettingDTO,
  options: API.ManasAPIOptions = {}
): Promise<API.Response<boolean>> => {
  return manasAPI<boolean>(portalApi, 'home/editHomeSetting', data, {
    ...options, 
    ajaxOptions: {
      type: 'POST',
      ...options.ajaxOptions
    }
  });
}

// 获取AI配置
export const getAiToken = async (): Promise<API.Response<string>> => {
  return manasAPI<string>(portalApi, 'ai/getToken', {}, {
    ajaxOptions: { type: 'GET'}
  })
}